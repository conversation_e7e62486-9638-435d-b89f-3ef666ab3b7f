{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/MergeNovels.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface Novel {\n  id: string;\n  title: string;\n  filename: string;\n  createdAt: string;\n  chapterCount?: number;\n  rewrittenChaptersCount?: number;\n}\n\ninterface MergeResult {\n  filePath: string;\n  novelTitle: string;\n}\n\nexport default function MergeNovels() {\n  const [novels, setNovels] = useState<Novel[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [merging, setMerging] = useState<string | null>(null);\n  const [mergeResults, setMergeResults] = useState<MergeResult[]>([]);\n\n  // 获取可合并的小说列表\n  const fetchNovels = async () => {\n    try {\n      const response = await fetch('/api/merge');\n      const result = await response.json();\n      \n      if (result.success) {\n        setNovels(result.data);\n      } else {\n        console.error('获取小说列表失败:', result.error);\n      }\n    } catch (error) {\n      console.error('获取小说列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 合并小说章节\n  const mergeNovel = async (novelId: string) => {\n    setMerging(novelId);\n    \n    try {\n      const response = await fetch('/api/merge', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ novelId }),\n      });\n      \n      const result = await response.json();\n      \n      if (result.success) {\n        setMergeResults(prev => [...prev, result.data]);\n        // 刷新列表\n        await fetchNovels();\n      } else {\n        alert(`合并失败: ${result.error}`);\n      }\n    } catch (error) {\n      console.error('合并失败:', error);\n      alert('合并失败');\n    } finally {\n      setMerging(null);\n    }\n  };\n\n  useEffect(() => {\n    fetchNovels();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"text-lg\">加载中...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">合并改写章节</h1>\n        <p className=\"text-gray-600\">将改写完成的章节合并为完整的小说文件</p>\n      </div>\n\n      {/* 可合并的小说列表 */}\n      <div className=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n        <h2 className=\"text-xl font-semibold mb-4\">可合并的小说</h2>\n        \n        {novels.length === 0 ? (\n          <div className=\"text-center py-8 text-gray-500\">\n            暂无可合并的小说\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {novels.map((novel) => (\n              <div key={novel.id} className=\"border rounded-lg p-4 flex justify-between items-center\">\n                <div>\n                  <h3 className=\"font-medium text-lg\">{novel.title}</h3>\n                  <p className=\"text-sm text-gray-600\">\n                    已改写章节: {novel.rewrittenChaptersCount} / 总章节: {novel.chapterCount || 0}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    创建时间: {new Date(novel.createdAt).toLocaleString()}\n                  </p>\n                </div>\n                \n                <button\n                  onClick={() => mergeNovel(novel.id)}\n                  disabled={merging === novel.id}\n                  className={`px-4 py-2 rounded-md font-medium ${\n                    merging === novel.id\n                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                      : 'bg-blue-600 text-white hover:bg-blue-700'\n                  }`}\n                >\n                  {merging === novel.id ? '合并中...' : '合并章节'}\n                </button>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* 合并结果 */}\n      {mergeResults.length > 0 && (\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\n          <h2 className=\"text-xl font-semibold mb-4\">合并结果</h2>\n          \n          <div className=\"space-y-3\">\n            {mergeResults.map((result, index) => (\n              <div key={index} className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <svg className=\"h-5 w-5 text-green-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                  <div className=\"ml-3\">\n                    <h3 className=\"text-sm font-medium text-green-800\">\n                      成功合并《{result.novelTitle}》\n                    </h3>\n                    <p className=\"text-sm text-green-700 mt-1\">\n                      文件保存在: {result.filePath}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAkBe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAU,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAgB;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAgB,EAAE;IAElE,aAAa;IACb,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,OAAO,IAAI;YACvB,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,OAAO,KAAK;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,aAAa,OAAO;QACxB,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAQ;YACjC;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,gBAAgB,CAAA,OAAQ;2BAAI;wBAAM,OAAO,IAAI;qBAAC;gBAC9C,OAAO;gBACP,MAAM;YACR,OAAO;gBACL,MAAM,CAAC,MAAM,EAAE,OAAO,KAAK,EAAE;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAA,kNAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;oBAE1C,OAAO,MAAM,KAAK,kBACjB,8OAAC;wBAAI,WAAU;kCAAiC;;;;;6CAIhD,8OAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gCAAmB,WAAU;;kDAC5B,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAuB,MAAM,KAAK;;;;;;0DAChD,8OAAC;gDAAE,WAAU;;oDAAwB;oDAC3B,MAAM,sBAAsB;oDAAC;oDAAS,MAAM,YAAY,IAAI;;;;;;;0DAEtE,8OAAC;gDAAE,WAAU;;oDAAwB;oDAC5B,IAAI,KAAK,MAAM,SAAS,EAAE,cAAc;;;;;;;;;;;;;kDAInD,8OAAC;wCACC,SAAS,IAAM,WAAW,MAAM,EAAE;wCAClC,UAAU,YAAY,MAAM,EAAE;wCAC9B,WAAW,CAAC,iCAAiC,EAC3C,YAAY,MAAM,EAAE,GAChB,iDACA,4CACJ;kDAED,YAAY,MAAM,EAAE,GAAG,WAAW;;;;;;;+BApB7B,MAAM,EAAE;;;;;;;;;;;;;;;;YA6BzB,aAAa,MAAM,GAAG,mBACrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAE3C,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,8OAAC;gCAAgB,WAAU;0CACzB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAyB,SAAQ;gDAAY,MAAK;0DAC/D,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAwI,UAAS;;;;;;;;;;;;;;;;sDAGhL,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;wDAAqC;wDAC3C,OAAO,UAAU;wDAAC;;;;;;;8DAE1B,8OAAC;oDAAE,WAAU;;wDAA8B;wDACjC,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;+BAZrB;;;;;;;;;;;;;;;;;;;;;;AAuBxB", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}