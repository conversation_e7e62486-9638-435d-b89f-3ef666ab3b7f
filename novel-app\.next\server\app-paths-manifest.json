{"/api/chapters/route": "app/api/chapters/route.js", "/api/characters/route": "app/api/characters/route.js", "/api/context/analyze/route": "app/api/context/analyze/route.js", "/api/context/chapter/route": "app/api/context/chapter/route.js", "/api/context/novel/route": "app/api/context/novel/route.js", "/api/jobs/route": "app/api/jobs/route.js", "/api/merge/route": "app/api/merge/route.js", "/api/novels/route": "app/api/novels/route.js", "/api/presets/route": "app/api/presets/route.js", "/api/rewrite/diagnostics/route": "app/api/rewrite/diagnostics/route.js", "/api/rewrite/route": "app/api/rewrite/route.js", "/context/page": "app/context/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/merge/page": "app/merge/page.js", "/page": "app/page.js"}