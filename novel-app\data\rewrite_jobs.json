[{"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 40, "completedChapters": 39, "failedChapters": 1, "totalTokensUsed": 191142, "totalProcessingTime": 69146, "averageTimePerChapter": 1728.65, "apiKeyStats": [{"name": "My First Project", "requestCount": 7, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 10, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 8, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 9, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 6, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4032, "processingTime": 7446}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4609, "processingTime": 6326}, {"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 3882, "processingTime": 7032}, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 3950, "processingTime": 8050}, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4162, "processingTime": 4691}, {"chapterNumber": 6, "chapterTitle": "第六章 小殿下", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4874, "processingTime": 6239}, {"chapterNumber": 7, "chapterTitle": "第七章 三更", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4235, "processingTime": 6619}, {"chapterNumber": 8, "chapterTitle": "第八章 榕树与日落", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4738, "processingTime": 6776}, {"chapterNumber": 9, "chapterTitle": "第九章 刀剑入夜", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3800, "processingTime": 2921}, {"chapterNumber": 10, "chapterTitle": "第十章 一纸空梦为谁书", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 6235, "processingTime": 13628}, {"chapterNumber": 11, "chapterTitle": "第十一章 殿下入井去，仙人乘轿来", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 7160, "processingTime": 9380}, {"chapterNumber": 12, "chapterTitle": "第十二章 妖雀鸣城", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "Generative Language Client", "processingTime": 470}, {"chapterNumber": 13, "chapterTitle": "第十三章 仙子悬剑气如虹", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 4459, "processingTime": 6134}, {"chapterNumber": 14, "chapterTitle": "第十四章 湖上狐影", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 4626, "processingTime": 4113}, {"chapterNumber": 15, "chapterTitle": "第十五章 我为杀局，请君入瓮", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 5125, "processingTime": 6199}, {"chapterNumber": 16, "chapterTitle": "第十六章 一个小道士的故事", "success": true, "apiKeyUsed": "chat", "tokensUsed": 5914, "processingTime": 9868}, {"chapterNumber": 17, "chapterTitle": "第十七章 皇宫下的背影", "success": true, "apiKeyUsed": "chat", "tokensUsed": 5994, "processingTime": 9012}, {"chapterNumber": 18, "chapterTitle": "第十八章 老狐一炬", "success": true, "apiKeyUsed": "chat", "tokensUsed": 4631, "processingTime": 1990}, {"chapterNumber": 19, "chapterTitle": "第十九章 一身白衣入城来", "success": true, "apiKeyUsed": "chat", "tokensUsed": 5396, "processingTime": 5015}, {"chapterNumber": 20, "chapterTitle": "第二十章 苏醒", "success": true, "apiKeyUsed": "chat", "tokensUsed": 3716, "processingTime": 5952}, {"chapterNumber": 21, "chapterTitle": "第二十一章 境界", "success": true, "apiKeyUsed": "chat", "tokensUsed": 3883, "processingTime": 7729}, {"chapterNumber": 22, "chapterTitle": "第二十二章 朱雀掠影焚天火", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 4975, "processingTime": 3576}, {"chapterNumber": 23, "chapterTitle": "第二十三章 秋雨肃杀", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 4016, "processingTime": 4355}, {"chapterNumber": 24, "chapterTitle": "第二十四章 狐影随形", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 7510, "processingTime": 17769}, {"chapterNumber": 25, "chapterTitle": "未命名章节", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 5052, "processingTime": 3106}, {"chapterNumber": 26, "chapterTitle": "第二十六章 夜幕降临之前", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 4060, "processingTime": 3447}, {"chapterNumber": 27, "chapterTitle": "第二十七章 城楼之下谪仙人", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 6045, "processingTime": 13957}, {"chapterNumber": 28, "chapterTitle": "第二十八章 城国之间，朱雀焚火", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 5810, "processingTime": 8247}, {"chapterNumber": 29, "chapterTitle": "未命名章节", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 4481, "processingTime": 3897}, {"chapterNumber": 30, "chapterTitle": "第三十章 风雪十六载，雨停烟花尽", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 4210, "processingTime": 2526}, {"chapterNumber": 31, "chapterTitle": "第三十一章 就像是一场梦", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4077, "processingTime": 2816}, {"chapterNumber": 32, "chapterTitle": "未命名章节", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4731, "processingTime": 3286}, {"chapterNumber": 33, "chapterTitle": "第三十三章 妖种", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5418, "processingTime": 10146}, {"chapterNumber": 34, "chapterTitle": "未命名章节", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5533, "processingTime": 7089}, {"chapterNumber": 35, "chapterTitle": "第三十五章 仙剑来时", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4447, "processingTime": 6590}, {"chapterNumber": 36, "chapterTitle": "第三十六章 云至劫来", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5107, "processingTime": 9967}, {"chapterNumber": 37, "chapterTitle": "第三十七章 心魔历劫", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4581, "processingTime": 8700}, {"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 3828, "processingTime": 4932}, {"chapterNumber": 39, "chapterTitle": "第三十九章 白雪如梦，华裳如炬", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 7159, "processingTime": 14566}, {"chapterNumber": 40, "chapterTitle": "第四十章 心魔领域里的小女孩", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 4681, "processingTime": 5498}], "model": "gemini-2.5-flash", "concurrency": 5}, "id": "mfxwdhwfirw8kkyqr28", "createdAt": "2025-09-24T11:25:06.399Z", "updatedAt": "2025-09-24T11:26:15.550Z", "result": "成功改写 39/40 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfxyqop5xgwa9w2fo3", "chapters": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 40, "completedChapters": 39, "failedChapters": 1, "totalTokensUsed": 191424, "totalProcessingTime": 134206, "averageTimePerChapter": 3355.15, "apiKeyStats": [{"name": "My First Project", "requestCount": 17, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 17, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 16, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 14, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 16, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4213, "processingTime": 16444}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4494, "processingTime": 12081}, {"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4016, "processingTime": 21762}, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3739, "processingTime": 7020}, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4469, "processingTime": 7695}, {"chapterNumber": 6, "chapterTitle": "第六章 小殿下", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5297, "processingTime": 10480}, {"chapterNumber": 7, "chapterTitle": "第七章 三更", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4395, "processingTime": 7433}, {"chapterNumber": 8, "chapterTitle": "第八章 榕树与日落", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4653, "processingTime": 8649}, {"chapterNumber": 9, "chapterTitle": "第九章 刀剑入夜", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3799, "processingTime": 4267}, {"chapterNumber": 10, "chapterTitle": "第十章 一纸空梦为谁书", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 6247, "processingTime": 17413}, {"chapterNumber": 11, "chapterTitle": "第十一章 殿下入井去，仙人乘轿来", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 7310, "processingTime": 13840}, {"chapterNumber": 12, "chapterTitle": "第十二章 妖雀鸣城", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "Generative Language Client", "processingTime": 467}, {"chapterNumber": 13, "chapterTitle": "第十三章 仙子悬剑气如虹", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4396, "processingTime": 9457}, {"chapterNumber": 14, "chapterTitle": "第十四章 湖上狐影", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4377, "processingTime": 2944}, {"chapterNumber": 15, "chapterTitle": "第十五章 我为杀局，请君入瓮", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4997, "processingTime": 7143}, {"chapterNumber": 16, "chapterTitle": "第十六章 一个小道士的故事", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 6002, "processingTime": 11194}, {"chapterNumber": 17, "chapterTitle": "第十七章 皇宫下的背影", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 6176, "processingTime": 11862}, {"chapterNumber": 18, "chapterTitle": "第十八章 老狐一炬", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4620, "processingTime": 2204}, {"chapterNumber": 19, "chapterTitle": "第十九章 一身白衣入城来", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 5365, "processingTime": 4778}, {"chapterNumber": 20, "chapterTitle": "第二十章 苏醒", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3997, "processingTime": 7196}, {"chapterNumber": 21, "chapterTitle": "第二十一章 境界", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4890, "processingTime": 15694}, {"chapterNumber": 22, "chapterTitle": "第二十二章 朱雀掠影焚天火", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 5117, "processingTime": 6162}, {"chapterNumber": 23, "chapterTitle": "第二十三章 秋雨肃杀", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3882, "processingTime": 5155}, {"chapterNumber": 24, "chapterTitle": "第二十四章 狐影随形", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 7596, "processingTime": 20068}, {"chapterNumber": 25, "chapterTitle": "第二十五章 长街有雨，青衫接剑", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5164, "processingTime": 4971}, {"chapterNumber": 26, "chapterTitle": "第二十六章 夜幕降临之前", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4513, "processingTime": 6512}, {"chapterNumber": 27, "chapterTitle": "第二十七章 城楼之下谪仙人", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 5014, "processingTime": 11257}, {"chapterNumber": 28, "chapterTitle": "第二十八章 城国之间，朱雀焚火", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 5825, "processingTime": 8998}, {"chapterNumber": 29, "chapterTitle": "第二十九章 天雷地火渐尾声", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4443, "processingTime": 4815}, {"chapterNumber": 30, "chapterTitle": "第三十章 风雪十六载，雨停烟花尽", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4753, "processingTime": 6453}, {"chapterNumber": 31, "chapterTitle": "第三十一章 就像是一场梦", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4058, "processingTime": 3217}, {"chapterNumber": 32, "chapterTitle": "第三十二章 婚书", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4662, "processingTime": 3338}, {"chapterNumber": 33, "chapterTitle": "第三十三章 妖种", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5580, "processingTime": 10691}, {"chapterNumber": 34, "chapterTitle": "第三十四章 小院之战", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4802, "processingTime": 4730}, {"chapterNumber": 35, "chapterTitle": "第三十五章 仙剑来时", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4508, "processingTime": 8185}, {"chapterNumber": 36, "chapterTitle": "第三十六章 云至劫来", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4348, "processingTime": 6097}, {"chapterNumber": 37, "chapterTitle": "第三十七章 心魔历劫", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4542, "processingTime": 7487}, {"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3791, "processingTime": 4643}, {"chapterNumber": 39, "chapterTitle": "第三十九章 白雪如梦，华裳如炬", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 7195, "processingTime": 16501}, {"chapterNumber": 40, "chapterTitle": "第四十章 心魔领域里的小女孩", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 4179, "processingTime": 3982}], "model": "gemini-2.5-flash", "concurrency": 3}, "id": "mfy39elul1jvtiosbtc", "createdAt": "2025-09-24T14:37:52.818Z", "updatedAt": "2025-09-24T14:40:07.030Z", "result": "成功改写 39/40 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 40, "completedChapters": 39, "failedChapters": 1, "totalTokensUsed": 278860, "totalProcessingTime": 132555, "averageTimePerChapter": 3313.875, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5706, "processingTime": 5817}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 7319, "processingTime": 8681}, {"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 6219, "processingTime": 9569}, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 6307, "processingTime": 8342}, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 6399, "processingTime": 4202}, {"chapterNumber": 6, "chapterTitle": "第六章 小殿下", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 7475, "processingTime": 8828}, {"chapterNumber": 7, "chapterTitle": "第七章 三更", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 6298, "processingTime": 7083}, {"chapterNumber": 8, "chapterTitle": "第八章 榕树与日落", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 5988, "processingTime": 3649}, {"chapterNumber": 9, "chapterTitle": "第九章 刀剑入夜", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 5846, "processingTime": 4276}, {"chapterNumber": 10, "chapterTitle": "第十章 一纸空梦为谁书", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 7103, "processingTime": 5895}, {"chapterNumber": 11, "chapterTitle": "第十一章 殿下入井去，仙人乘轿来", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 9116, "processingTime": 8977}, {"chapterNumber": 12, "chapterTitle": "第十二章 妖雀鸣城", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 7418, "processingTime": 5897}, {"chapterNumber": 13, "chapterTitle": "第十三章 仙子悬剑气如虹", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 6588, "processingTime": 7203}, {"chapterNumber": 14, "chapterTitle": "第十四章 湖上狐影", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 6141, "processingTime": 2455}, {"chapterNumber": 15, "chapterTitle": "第十五章 我为杀局，请君入瓮", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 8472, "processingTime": 12446}, {"chapterNumber": 16, "chapterTitle": "第十六章 一个小道士的故事", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 8122, "processingTime": 12057}, {"chapterNumber": 17, "chapterTitle": "第十七章 皇宫下的背影", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 7727, "processingTime": 8502}, {"chapterNumber": 18, "chapterTitle": "第十八章 老狐一炬", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 6358, "processingTime": 2053}, {"chapterNumber": 19, "chapterTitle": "第十九章 一身白衣入城来", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 7370, "processingTime": 5271}, {"chapterNumber": 20, "chapterTitle": "第二十章 苏醒", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 6123, "processingTime": 9708}, {"chapterNumber": 21, "chapterTitle": "第二十一章 境界", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 6800, "processingTime": 12781}, {"chapterNumber": 22, "chapterTitle": "第二十二章 朱雀掠影焚天火", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 7734, "processingTime": 8991}, {"chapterNumber": 23, "chapterTitle": "第二十三章 秋雨肃杀", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 5857, "processingTime": 3175}, {"chapterNumber": 24, "chapterTitle": "第二十四章 狐影随形", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 9692, "processingTime": 16517}, {"chapterNumber": 25, "chapterTitle": "第二十五章 长街有雨，青衫接剑", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 8968, "processingTime": 13455}, {"chapterNumber": 26, "chapterTitle": "第二十六章 夜幕降临之前", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 6636, "processingTime": 6820}, {"chapterNumber": 27, "chapterTitle": "第二十七章 城楼之下谪仙人", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 8074, "processingTime": 12954}, {"chapterNumber": 28, "chapterTitle": "第二十八章 城国之间，朱雀焚火", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 7765, "processingTime": 9239}, {"chapterNumber": 29, "chapterTitle": "第二十九章 天雷地火渐尾声", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 6646, "processingTime": 7254}, {"chapterNumber": 30, "chapterTitle": "第三十章 风雪十六载，雨停烟花尽", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 6638, "processingTime": 6215}, {"chapterNumber": 31, "chapterTitle": "第三十一章 就像是一场梦", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 6818, "processingTime": 7768}, {"chapterNumber": 32, "chapterTitle": "第三十二章 婚书", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 7550, "processingTime": 7355}, {"chapterNumber": 33, "chapterTitle": "第三十三章 妖种", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 7311, "processingTime": 8927}, {"chapterNumber": 34, "chapterTitle": "第三十四章 小院之战", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 6280, "processingTime": 3931}, {"chapterNumber": 35, "chapterTitle": "第三十五章 仙剑来时", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 7060, "processingTime": 10982}, {"chapterNumber": 36, "chapterTitle": "第三十六章 云至劫来", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 6978, "processingTime": 9751}, {"chapterNumber": 37, "chapterTitle": "第三十七章 心魔历劫", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 6784, "processingTime": 7529}, {"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "chat", "processingTime": 62627}, {"chapterNumber": 39, "chapterTitle": "第三十九章 白雪如梦，华裳如炬", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 8549, "processingTime": 10181}, {"chapterNumber": 40, "chapterTitle": "第四十章 心魔领域里的小女孩", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 8625, "processingTime": 15779}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz19lf8uf6ay0qevk", "createdAt": "2025-09-25T06:29:48.596Z", "updatedAt": "2025-09-25T06:32:01.153Z", "result": "成功改写 39/40 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32933, "averageTimePerChapter": 32933, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "In The Novel", "processingTime": 32928}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz1f7ykhz9cnptj9ir", "createdAt": "2025-09-25T06:34:11.084Z", "updatedAt": "2025-09-25T06:34:44.019Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32414, "averageTimePerChapter": 32414, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "Generative Language Client", "processingTime": 32406}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz1jnahl163g70ge7", "createdAt": "2025-09-25T06:37:37.577Z", "updatedAt": "2025-09-25T06:38:09.993Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 5845, "totalProcessingTime": 12261, "averageTimePerChapter": 12261, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5845, "processingTime": 12252}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz1s3204fz7var53wa", "createdAt": "2025-09-25T06:44:11.256Z", "updatedAt": "2025-09-25T06:44:23.518Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32382, "averageTimePerChapter": 32382, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "processingTime": 32377}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz1t73kaxxj8xdexnu", "createdAt": "2025-09-25T06:45:03.152Z", "updatedAt": "2025-09-25T06:45:35.535Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 33322, "averageTimePerChapter": 33322, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "chat", "processingTime": 33315}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz1wv0s8brqpafvfdb", "createdAt": "2025-09-25T06:47:54.124Z", "updatedAt": "2025-09-25T06:48:27.448Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 5731, "totalProcessingTime": 8127, "averageTimePerChapter": 8127, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5731, "processingTime": 8121}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz1ypw5jitbj3344op", "createdAt": "2025-09-25T06:49:20.789Z", "updatedAt": "2025-09-25T06:49:28.923Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32974, "averageTimePerChapter": 32974, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "In The Novel", "processingTime": 32968}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz205wfeqy3lx933x", "createdAt": "2025-09-25T06:50:28.191Z", "updatedAt": "2025-09-25T06:51:01.168Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32840, "averageTimePerChapter": 32840, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "My First Project", "processingTime": 32831}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz2o8719glc2bwlcpb", "createdAt": "2025-09-25T07:09:10.909Z", "updatedAt": "2025-09-25T07:09:43.751Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32985, "averageTimePerChapter": 32985, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "chat", "processingTime": 32977}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz2svbw4vgvlaso0u8", "createdAt": "2025-09-25T07:12:47.516Z", "updatedAt": "2025-09-25T07:13:20.503Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32439, "averageTimePerChapter": 32439, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "In The Novel", "processingTime": 32430}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz34fkfvu9qhkdkco", "createdAt": "2025-09-25T07:21:46.959Z", "updatedAt": "2025-09-25T07:22:19.401Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32940, "averageTimePerChapter": 32940, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "chat", "processingTime": 32930}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz36kqjorunpvyx3sa", "createdAt": "2025-09-25T07:23:26.971Z", "updatedAt": "2025-09-25T07:23:59.914Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [3], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 6221, "totalProcessingTime": 9998, "averageTimePerChapter": 9998, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 6221, "processingTime": 9990}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz39l7q7llzqqk1sxc", "createdAt": "2025-09-25T07:25:47.558Z", "updatedAt": "2025-09-25T07:25:57.559Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 7594, "totalProcessingTime": 15470, "averageTimePerChapter": 15470, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 7594, "processingTime": 15461}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz52sk5z3mpbof6f1p", "createdAt": "2025-09-25T08:16:29.717Z", "updatedAt": "2025-09-25T08:16:45.189Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 6343, "totalProcessingTime": 16786, "averageTimePerChapter": 16786, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 6343, "processingTime": 16779}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz53rga860r8ktx82f", "createdAt": "2025-09-25T08:17:14.938Z", "updatedAt": "2025-09-25T08:17:31.726Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 6160, "totalProcessingTime": 8447, "averageTimePerChapter": 8447, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 6160, "processingTime": 8436}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz5b8bgrntgckayxvk", "createdAt": "2025-09-25T08:23:03.388Z", "updatedAt": "2025-09-25T08:23:11.838Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}]